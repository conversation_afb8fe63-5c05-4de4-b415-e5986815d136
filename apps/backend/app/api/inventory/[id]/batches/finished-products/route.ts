import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const batches = await prisma.finishedProductBatch.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(batches);
  } catch (error) {
    console.error('Error fetching finished product batches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch finished product batches' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    const batch = await prisma.finishedProductBatch.create({
      data: {
        ...data,
        inventoryItemId: id,
      },
    });

    return NextResponse.json(batch, { status: 201 });
  } catch (error) {
    console.error('Error creating finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to create finished product batch' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { batchId, ...data } = await request.json();

    const batch = await prisma.finishedProductBatch.update({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(batch);
  } catch (error) {
    console.error('Error updating finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to update finished product batch' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    await prisma.finishedProductBatch.delete({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to delete finished product batch' },
      { status: 500 }
    );
  }
}
