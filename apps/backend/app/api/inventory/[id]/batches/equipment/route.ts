import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const records = await prisma.equipmentRecord.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(records);
  } catch (error) {
    console.error('Error fetching equipment records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch equipment records' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    const record = await prisma.equipmentRecord.create({
      data: {
        ...data,
        inventoryItemId: id,
      },
    });

    return NextResponse.json(record, { status: 201 });
  } catch (error) {
    console.error('Error creating equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to create equipment record' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { recordId, ...data } = await request.json();

    const record = await prisma.equipmentRecord.update({
      where: { 
        id: recordId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(record);
  } catch (error) {
    console.error('Error updating equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to update equipment record' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const recordId = searchParams.get('recordId');

    if (!recordId) {
      return NextResponse.json(
        { error: 'Record ID is required' },
        { status: 400 }
      );
    }

    await prisma.equipmentRecord.delete({
      where: { 
        id: recordId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to delete equipment record' },
      { status: 500 }
    );
  }
}
