const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { id: 'cmcl1e6l70000v1kcq5hf8v55' }
    });
    
    if (user) {
      console.log('✅ User found:', user);
    } else {
      console.log('❌ User not found');
      
      // List all users
      const allUsers = await prisma.user.findMany();
      console.log('All users in database:', allUsers);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
