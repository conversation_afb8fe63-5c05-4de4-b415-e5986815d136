// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and business context
model User {
  id           String      @id @default(cuid())
  name         String
  email        String      @unique
  businessType BusinessType
  companyName  String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  projects     Project[]
  teamMembers  TeamMember[]
  catalogItems CatalogItem[]
  inventoryItems InventoryItem[]
  financialRecords FinancialRecord[]
  financialMovements FinancialMovement[]

  @@map("users")
}

enum BusinessType {
  fabrication
  retail
  services
  manufacturing
  distribution
  construction
}

// Team Management
model TeamMember {
  id         String   @id @default(cuid())
  name       String
  role       String
  email      String
  avatar     String?
  status     MemberStatus @default(active)
  salary     Float
  currency   String   @default("USD")
  hourlyRate Float?
  skills     String[] // Array of skills
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Many-to-many with projects
  projects   ProjectTeamMember[]
  
  // Tasks assigned to this member
  tasks      ProjectTask[]

  @@map("team_members")
}

enum MemberStatus {
  active
  inactive
}

// Catalog Management
model CatalogItem {
  id                 String   @id @default(cuid())
  productName        String
  productDescription String
  categoryLabel      CategoryLabel
  imageSrc           String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  userId             String
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  details            CatalogItemDetails?

  @@map("catalog_items")
}

enum CategoryLabel {
  Product
  Service
}

model CatalogItemDetails {
  id                    String   @id @default(cuid())
  title                 String
  type                  CategoryLabel
  
  // Product-specific fields
  fabrication           String?
  
  // Service-specific fields
  duration              String?
  requirements          String[] // Array of requirements
  serviceType           String?  // References service template
  serviceSpecifications Json?    // Dynamic fields based on service template
  
  // Common fields
  images                String[] // Array of image URLs
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  catalogItemId         String   @unique
  catalogItem           CatalogItem @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
  
  materials             Material[]
  deliverables          Deliverable[]
  documents             Document[]

  @@map("catalog_item_details")
}

model Material {
  id                    String   @id @default(cuid())
  name                  String
  material              String
  quantity              Float
  unit                  String
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  catalogItemDetailsId  String
  catalogItemDetails    CatalogItemDetails @relation(fields: [catalogItemDetailsId], references: [id], onDelete: Cascade)

  @@map("materials")
}

model Deliverable {
  id                    String   @id @default(cuid())
  name                  String
  description           String
  timeline              String
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  catalogItemDetailsId  String
  catalogItemDetails    CatalogItemDetails @relation(fields: [catalogItemDetailsId], references: [id], onDelete: Cascade)

  @@map("deliverables")
}

model Document {
  id                    String   @id @default(cuid())
  name                  String
  url                   String
  type                  String
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  catalogItemDetailsId  String
  catalogItemDetails    CatalogItemDetails @relation(fields: [catalogItemDetailsId], references: [id], onDelete: Cascade)

  @@map("documents")
}

// Inventory Management
model InventoryItem {
  id              String   @id @default(cuid())
  name            String
  description     String
  quantity        Float
  unit            String
  category        String
  categoryId      String   // References inventory category from templates
  minStock        Float?
  maxStock        Float?
  cost            Float
  location        String?
  supplier        String?
  additionalFields Json?   // Dynamic fields based on category
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("inventory_items")
}

// Financial Management
model FinancialRecord {
  id          String   @id @default(cuid())
  type        RecordType
  amount      Float
  description String
  date        DateTime
  category    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("financial_records")
}

enum RecordType {
  income
  expense
}

model FinancialMovement {
  id                String   @id @default(cuid())
  fecha             DateTime // date
  concepto          String   // description/concept
  monto             Float    // amount
  tipo              MovementType // income/expense
  asignacion        String?  // assignment (project, etc.)
  categoria         String   // category
  comportamiento    String?  // behavior (Fijo, Variable, etc.)
  comprobante       String?  // receipt/document
  paymentMethod     PaymentMethod?
  isRecurring       Boolean  @default(false)
  dueDate           DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  recurringDetails  RecurringPaymentDetails?
  forecasts         ForecastEntry[]

  @@map("financial_movements")
}

enum MovementType {
  Entrada // income
  Salida  // expense
}

enum PaymentMethod {
  cash
  credit_card
  loan
  bank_transfer
  check
}

model RecurringPaymentDetails {
  id                    String   @id @default(cuid())
  monthlyAmount         Float    // Monthly payment amount
  totalMonths           Int      // Total number of months
  remainingMonths       Int      // Remaining months to pay
  interestRate          Float?   // Interest rate percentage
  startDate             DateTime // When payments started
  nextDueDate           DateTime // Next payment due date
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  financialMovementId   String   @unique
  financialMovement     FinancialMovement @relation(fields: [financialMovementId], references: [id], onDelete: Cascade)

  @@map("recurring_payment_details")
}

model ForecastEntry {
  id                    String   @id @default(cuid())
  date                  DateTime // YYYY-MM-DD format
  concepto              String   // description
  monto                 Float    // amount
  tipo                  MovementType // income/expense
  categoria             String   // category
  sourceType            ForecastSourceType // source of forecast
  sourceId              String?  // ID of the original movement/record
  isConfirmed           Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  userId                String?
  user                  User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  financialMovementId   String?
  financialMovement     FinancialMovement? @relation(fields: [financialMovementId], references: [id], onDelete: Cascade)
  recurringPaymentInfo  RecurringPaymentInfo?

  @@map("forecast_entries")
}

enum ForecastSourceType {
  recurring_payment
  scheduled_payment
  manual
}

model RecurringPaymentInfo {
  id              String   @id @default(cuid())
  monthNumber     Int      // which month in the sequence (1, 2, 3...)
  totalMonths     Int      // total months in the sequence
  remainingMonths Int      // remaining months after this one
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  forecastEntryId String   @unique
  forecastEntry   ForecastEntry @relation(fields: [forecastEntryId], references: [id], onDelete: Cascade)

  @@map("recurring_payment_info")
}

// Project Management
model Project {
  id          String        @id @default(cuid())
  name        String
  description String
  status      ProjectStatus @default(planning)
  startDate   DateTime
  endDate     DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  userId      String
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Many-to-many with team members
  teamMembers ProjectTeamMember[]

  // Project tasks
  tasks       ProjectTask[]

  // Project modules configuration
  modules     ProjectModules?

  @@map("projects")
}

enum ProjectStatus {
  planning
  in_progress
  completed
  on_hold
}

model ProjectTeamMember {
  id           String   @id @default(cuid())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  projectId    String
  project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  teamMemberId String
  teamMember   TeamMember @relation(fields: [teamMemberId], references: [id], onDelete: Cascade)

  @@unique([projectId, teamMemberId])
  @@map("project_team_members")
}

model ProjectTask {
  id          String   @id @default(cuid())
  name        String
  startDate   Int      // Day of month (1-31)
  endDate     Int      // Day of month (1-31)
  completed   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // Many-to-many with team members (assigned to)
  assignedTo  ProjectTaskAssignment[]

  @@map("project_tasks")
}

model ProjectTaskAssignment {
  id           String   @id @default(cuid())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  taskId       String
  task         ProjectTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  teamMemberId String
  teamMember   TeamMember  @relation(fields: [teamMemberId], references: [id], onDelete: Cascade)

  @@unique([taskId, teamMemberId])
  @@map("project_task_assignments")
}

model ProjectModules {
  id            String   @id @default(cuid())
  enabled       ProjectModuleType[]
  configuration Json?    // ModuleConfiguration as JSON
  aiRecommended ProjectModuleType[] // Modules recommended by AI
  userOverrides ProjectModuleType[] // User manual overrides
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  projectId     String   @unique
  project       Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("project_modules")
}

enum ProjectModuleType {
  finance    // Always required - financial tracking and budgeting
  catalog    // Products and/or services catalog
  team       // Team management and resource allocation
  timeline   // Project timeline and task management
  inventory  // Materials and inventory management
  logistics  // Logistics and supply chain management
}
