{"name": "@admin/backend", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@admin/design-tokens": "*", "@admin/ui": "*", "@prisma/client": "^5.22.0", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.23.8"}, "devDependencies": {"@admin/eslint-config": "*", "@admin/typescript-config": "*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.0", "eslint": "^9.28.0", "postcss": "^8.4.0", "prisma": "^5.22.0", "tailwindcss": "^3.4.0", "tsx": "^4.19.1", "typescript": "5.8.2"}}