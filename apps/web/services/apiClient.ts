import type {
  User,
  Project,
  TeamMember,
  CatalogItem,
  InventoryItem,
  FinancialRecord,
  FinancialMovement,
} from '@repo/ui/types';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Generic API client class
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Generic CRUD methods
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return this.request<T>(`${endpoint}${searchParams}`);
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete(endpoint: string): Promise<void> {
    await this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

// Create API client instance
const apiClient = new ApiClient(API_BASE_URL);

// API service functions that match the frontend DataStore interface
export const apiService = {
  // User operations
  users: {
    getAll: (): Promise<User[]> => apiClient.get('/users'),
    getById: (id: string): Promise<User> => apiClient.get(`/users/${id}`),
    create: (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> => 
      apiClient.post('/users', userData),
    update: (id: string, userData: Partial<User>): Promise<User> => 
      apiClient.put(`/users/${id}`, userData),
    delete: (id: string): Promise<void> => apiClient.delete(`/users/${id}`),
  },

  // Team member operations
  teamMembers: {
    getAll: (userId: string): Promise<TeamMember[]> => 
      apiClient.get('/team', { userId }),
    getById: (id: string): Promise<TeamMember> => apiClient.get(`/team/${id}`),
    create: (teamMemberData: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<TeamMember> => 
      apiClient.post('/team', teamMemberData),
    update: (id: string, teamMemberData: Partial<TeamMember>): Promise<TeamMember> => 
      apiClient.put(`/team/${id}`, teamMemberData),
    delete: (id: string): Promise<void> => apiClient.delete(`/team/${id}`),
  },

  // Project operations
  projects: {
    getAll: (userId: string): Promise<Project[]> => 
      apiClient.get('/projects', { userId }),
    getById: (id: string): Promise<Project> => apiClient.get(`/projects/${id}`),
    create: (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> => 
      apiClient.post('/projects', projectData),
    update: (id: string, projectData: Partial<Project>): Promise<Project> => 
      apiClient.put(`/projects/${id}`, projectData),
    delete: (id: string): Promise<void> => apiClient.delete(`/projects/${id}`),
  },

  // Catalog operations
  catalog: {
    getAll: (userId: string, type?: 'Product' | 'Service'): Promise<CatalogItem[]> => 
      apiClient.get('/catalog', { userId, ...(type && { type }) }),
    getById: (id: string): Promise<CatalogItem> => apiClient.get(`/catalog/${id}`),
    create: (catalogItemData: Omit<CatalogItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<CatalogItem> => 
      apiClient.post('/catalog', catalogItemData),
    update: (id: string, catalogItemData: Partial<CatalogItem>): Promise<CatalogItem> => 
      apiClient.put(`/catalog/${id}`, catalogItemData),
    delete: (id: string): Promise<void> => apiClient.delete(`/catalog/${id}`),
  },

  // Inventory operations
  inventory: {
    getAll: (userId: string, filters?: { category?: string; lowStock?: boolean }): Promise<InventoryItem[]> => 
      apiClient.get('/inventory', { userId, ...filters }),
    getById: (id: string): Promise<InventoryItem> => apiClient.get(`/inventory/${id}`),
    create: (inventoryItemData: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<InventoryItem> => 
      apiClient.post('/inventory', inventoryItemData),
    update: (id: string, inventoryItemData: Partial<InventoryItem>): Promise<InventoryItem> => 
      apiClient.put(`/inventory/${id}`, inventoryItemData),
    delete: (id: string): Promise<void> => apiClient.delete(`/inventory/${id}`),
  },

  // Finance operations
  finance: {
    getAll: (userId: string, type?: 'records' | 'movements' | 'forecasts' | 'all'): Promise<{
      records?: FinancialRecord[];
      movements?: FinancialMovement[];
      forecasts?: any[];
    }> => apiClient.get('/finance', { userId, ...(type && { type }) }),
    getById: (id: string, type: 'record' | 'movement'): Promise<FinancialRecord | FinancialMovement> =>
      apiClient.get(`/finance/${id}`, { type }),
    createRecord: (recordData: Omit<FinancialRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialRecord> =>
      apiClient.post('/finance', { type: 'record', ...recordData }),
    createMovement: (movementData: Omit<FinancialMovement, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialMovement> =>
      apiClient.post('/finance', { type: 'movement', ...movementData }),
    updateRecord: (id: string, recordData: Partial<FinancialRecord>): Promise<FinancialRecord> =>
      apiClient.put(`/finance/${id}`, { type: 'record', ...recordData }),
    updateMovement: (id: string, movementData: Partial<FinancialMovement>): Promise<FinancialMovement> =>
      apiClient.put(`/finance/${id}`, { type: 'movement', ...movementData }),
    deleteRecord: (id: string): Promise<void> => apiClient.delete(`/finance/${id}?type=record`),
    deleteMovement: (id: string): Promise<void> => apiClient.delete(`/finance/${id}?type=movement`),
  },

  // Context data operations
  context: {
    getContextData: (userId: string, editType: string, filter?: string): Promise<{
      items: any[];
      totalCount: number;
      categories?: string[];
      hasMore?: boolean;
    }> => apiClient.get('/context', { userId, editType, ...(filter && { filter }) }),
  },
};

export default apiService;
