import apiService from './apiClient';
import type { TeamMember, CatalogItem, InventoryItem } from '@repo/ui/types';

// Backend-powered context data service that replaces the mock implementation
export class BackendContextDataService {
  private currentUserId: string = 'default-user-id'; // This should be set from auth context

  // Set the current user ID (should be called from auth context)
  setCurrentUserId(userId: string) {
    this.currentUserId = userId;
  }

  getCurrentUserId(): string {
    return this.currentUserId;
  }

  // Fetch team members for context selection
  async fetchTeamMembers(): Promise<TeamMember[]> {
    return apiService.teamMembers.getAll(this.currentUserId);
  }

  // Fetch catalog items for context selection
  async fetchCatalogItems(type?: 'Product' | 'Service'): Promise<CatalogItem[]> {
    return apiService.catalog.getAll(this.currentUserId, type);
  }

  // Fetch inventory items for context selection
  async fetchInventoryItems(category?: string): Promise<InventoryItem[]> {
    const filters = category ? { category } : undefined;
    return apiService.inventory.getAll(this.currentUserId, filters);
  }

  // Get context data for AI project editing
  async getContextData(editType: string, filter?: string): Promise<{
    items: any[];
    totalCount: number;
    categories?: string[];
    hasMore?: boolean;
  }> {
    return apiService.context.getContextData(this.currentUserId, editType, filter);
  }

  // Search across all context types
  async searchContextItems(
    query: string,
    types: ('team_member' | 'catalog_item' | 'inventory_item')[] = ['team_member', 'catalog_item', 'inventory_item']
  ): Promise<{
    teamMembers: TeamMember[];
    catalogItems: CatalogItem[];
    inventoryItems: InventoryItem[];
  }> {
    const results = {
      teamMembers: [] as TeamMember[],
      catalogItems: [] as CatalogItem[],
      inventoryItems: [] as InventoryItem[],
    };

    const searchQuery = query.toLowerCase();

    // Search team members
    if (types.includes('team_member')) {
      const allTeamMembers = await this.fetchTeamMembers();
      results.teamMembers = allTeamMembers.filter(member =>
        member.name.toLowerCase().includes(searchQuery) ||
        member.role.toLowerCase().includes(searchQuery) ||
        member.email.toLowerCase().includes(searchQuery) ||
        (member.skills && member.skills.some(skill => 
          skill.toLowerCase().includes(searchQuery)
        ))
      );
    }

    // Search catalog items
    if (types.includes('catalog_item')) {
      const allCatalogItems = await this.fetchCatalogItems();
      results.catalogItems = allCatalogItems.filter(item =>
        item.productName.toLowerCase().includes(searchQuery) ||
        item.productDescription.toLowerCase().includes(searchQuery) ||
        item.categoryLabel.toLowerCase().includes(searchQuery)
      );
    }

    // Search inventory items
    if (types.includes('inventory_item')) {
      const allInventoryItems = await this.fetchInventoryItems();
      results.inventoryItems = allInventoryItems.filter(item =>
        item.name.toLowerCase().includes(searchQuery) ||
        item.description.toLowerCase().includes(searchQuery) ||
        item.category.toLowerCase().includes(searchQuery) ||
        (item.supplier && item.supplier.toLowerCase().includes(searchQuery))
      );
    }

    return results;
  }

  // Get available categories for filtering
  async getInventoryCategories(): Promise<string[]> {
    const allInventoryItems = await this.fetchInventoryItems();
    const categories = [...new Set(allInventoryItems.map(item => item.category))];
    return categories.sort();
  }

  // Get team member skills for filtering
  async getTeamMemberSkills(): Promise<string[]> {
    const allTeamMembers = await this.fetchTeamMembers();
    const allSkills = allTeamMembers.flatMap(member => member.skills || []);
    const uniqueSkills = [...new Set(allSkills)];
    return uniqueSkills.sort();
  }

  // Get team member roles for filtering
  async getTeamMemberRoles(): Promise<string[]> {
    const allTeamMembers = await this.fetchTeamMembers();
    const roles = [...new Set(allTeamMembers.map(member => member.role))];
    return roles.sort();
  }

  // Get low stock inventory items
  async getLowStockItems(): Promise<InventoryItem[]> {
    return apiService.inventory.getAll(this.currentUserId, { lowStock: true });
  }

  // Get team members by skill
  async getTeamMembersBySkill(skill: string): Promise<TeamMember[]> {
    const allTeamMembers = await this.fetchTeamMembers();
    return allTeamMembers.filter(member =>
      member.skills && member.skills.includes(skill)
    );
  }

  // Get available team members (not assigned to active projects)
  async getAvailableTeamMembers(): Promise<TeamMember[]> {
    const allTeamMembers = await this.fetchTeamMembers();
    // This is a simplified version - the backend API should handle this logic
    return allTeamMembers.filter(member => member.status === 'active');
  }

  // Get team members by role
  async getTeamMembersByRole(role: string): Promise<TeamMember[]> {
    const allTeamMembers = await this.fetchTeamMembers();
    return allTeamMembers.filter(member => member.role === role);
  }

  // Get catalog items by type
  async getCatalogItemsByType(type: 'Product' | 'Service'): Promise<CatalogItem[]> {
    return this.fetchCatalogItems(type);
  }

  // Get inventory items by category
  async getInventoryItemsByCategory(category: string): Promise<InventoryItem[]> {
    return this.fetchInventoryItems(category);
  }

  // Utility method to get all context data at once
  async getAllContextData(): Promise<{
    teamMembers: TeamMember[];
    catalogItems: CatalogItem[];
    inventoryItems: InventoryItem[];
    categories: {
      inventory: string[];
      teamRoles: string[];
      teamSkills: string[];
    };
  }> {
    const [
      teamMembers,
      catalogItems,
      inventoryItems,
      inventoryCategories,
      teamRoles,
      teamSkills,
    ] = await Promise.all([
      this.fetchTeamMembers(),
      this.fetchCatalogItems(),
      this.fetchInventoryItems(),
      this.getInventoryCategories(),
      this.getTeamMemberRoles(),
      this.getTeamMemberSkills(),
    ]);

    return {
      teamMembers,
      catalogItems,
      inventoryItems,
      categories: {
        inventory: inventoryCategories,
        teamRoles,
        teamSkills,
      },
    };
  }
}

// Export singleton instance
export const backendContextDataService = new BackendContextDataService();

// Export as default for easy replacement of the mock contextDataService
export default backendContextDataService;
