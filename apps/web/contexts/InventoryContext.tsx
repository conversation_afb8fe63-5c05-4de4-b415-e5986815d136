"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { InventoryContextState, InventoryItem, BusinessType } from './types';
import { backendDataStore } from '../services/backendDataStore';
import { useUser } from './UserContext';
import { fetchInventoryCategories, type InventoryCategory } from '../services/inventoryTemplates';


export interface RawMaterialBatch {
  id: string;
  batch: string;
  dimensions: string;
  quantity: number;
  unit: string;
  unit_cost: number;
  total_cost: number;
  purchase_date: string;
  supplier: string;
}

export interface FinishedProductBatch {
  id: string;
  batch?: string;
  serial_number?: string;
  dimensions?: string;
  model?: string;
  firmware_version?: string;
  material_finish?: string;
  specifications?: string;
  power_rating?: string;
  asset_tag?: string;
  quantity: number;
  unit: string;
  quality_status?: string;
  test_status?: string;
  operational_status?: string;
  unit_cost: number;
  sale_price: number;
  completion_date?: string;
  assembly_date?: string;
  customer?: string;
  warranty_period?: string;
  maintenance_schedule?: string;
}

export interface ConsumableBatch {
  id: string;
  batch: string;
  specifications?: string;
  tool_specifications?: string;
  chemical_composition?: string;
  concentration?: string;
  grit_size?: string;
  dimensions?: string;
  material_grade?: string;
  initial_quantity: number;
  current_quantity: number;
  unit: string;
  usage_percentage: number;
  unit_cost: number;
  purchase_date: string;
  expiry_date?: string;
  estimated_life?: string;
  safety_notes?: string;
  supplier: string;
}

export interface EquipmentRecord {
  id: string;
  asset_tag: string;
  model: string;
  serial_number: string;
  amperage_rating?: string;
  cutting_capacity?: string;
  power_rating?: string;
  voltage?: string;
  measurement_range?: string;
  accuracy?: string;
  certification?: string;
  protection_level?: string;
  condition_status: string;
  calibration_status?: string;
  operating_hours?: number;
  last_maintenance?: string;
  next_maintenance?: string;
  last_calibration?: string;
  next_calibration?: string;
  last_inspection?: string;
  next_inspection?: string;
  expiry_date?: string;
  assigned_to?: string;
  acquisition_cost: number;
  location?: string;
}

interface InventoryContextActions {
  getItemById: (id: string) => InventoryItem | undefined;
  refreshItems: () => Promise<void>;
  updateQuantity: (id: string, quantity: number) => void;
  getItemsByCategory: (categoryId: string) => InventoryItem[];
  getCategories: () => InventoryCategory[];
  loadCategoriesForBusinessType: (businessType: BusinessType) => Promise<void>;
  getRawMaterialBatches: (itemId: string) => Promise<RawMaterialBatch[]>;
  getFinishedProductBatches: (itemId: string) => Promise<FinishedProductBatch[]>;
  getConsumableBatches: (itemId: string) => Promise<ConsumableBatch[]>;
  getEquipmentRecords: (itemId: string) => Promise<EquipmentRecord[]>;
  addInventoryItem: (item: InventoryItem) => void;
  removeInventoryItem: (id: string) => InventoryItem | null;
  findItemByName: (name: string) => InventoryItem | null;
}

type InventoryContextType = InventoryContextState & InventoryContextActions & {
  categories: InventoryCategory[];
  categoriesLoading: boolean;
};

const InventoryContext = createContext<InventoryContextType | undefined>(undefined);

interface InventoryProviderProps {
  children: ReactNode;
}

export const InventoryProvider: React.FC<InventoryProviderProps> = ({ children }) => {
  const { user, loading: userLoading } = useUser();
  const [state, setState] = useState<InventoryContextState>({
    items: [],
    loading: true,
    error: null,
  });

  const [categories, setCategories] = useState<InventoryCategory[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // Fetch inventory items from backend
  const fetchInventoryItems = async (): Promise<InventoryItem[]> => {
    return await backendDataStore.getInventoryItems();
  };

  const refreshItems = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const items = await fetchInventoryItems();
      setState(prev => ({
        ...prev,
        items,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch inventory items',
      }));
    }
  };

  const getItemById = (id: string): InventoryItem | undefined => {
    return state.items.find(item => item.id === id);
  };

  const updateQuantity = (id: string, quantity: number) => {
    const currentDate = new Date().toISOString().split('T')[0] || new Date().toISOString();
    setState(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === id) {
          const updatedItem: InventoryItem = {
            ...item,
            quantity,
            lastUpdated: currentDate
          };
          return updatedItem;
        }
        return item;
      }),
    }));
  };

  const getItemsByCategory = useCallback((categoryId: string): InventoryItem[] => {
    return state.items.filter(item => item.categoryId === categoryId);
  }, [state.items]);

  const getCategories = useCallback((): InventoryCategory[] => {
    return categories;
  }, [categories]);

  const loadCategoriesForBusinessType = useCallback(async (businessType: BusinessType): Promise<void> => {
    setCategoriesLoading(true);
    try {
      const businessCategories = await fetchInventoryCategories(businessType);
      setCategories(businessCategories);
    } catch (error) {
      console.error('Failed to load inventory categories:', error);
    } finally {
      setCategoriesLoading(false);
    }
  }, []);

  const getRawMaterialBatches = useCallback(async (itemId: string): Promise<RawMaterialBatch[]> => {
    try {
      return await backendDataStore.getRawMaterialBatches(itemId);
    } catch (error) {
      console.error('Failed to fetch raw material batches:', error);
      return [];
    }
  }, []);

  const getFinishedProductBatches = useCallback(async (itemId: string): Promise<FinishedProductBatch[]> => {
    try {
      return await backendDataStore.getFinishedProductBatches(itemId);
    } catch (error) {
      console.error('Failed to fetch finished product batches:', error);
      return [];
    }
  }, []);

  const getConsumableBatches = useCallback(async (itemId: string): Promise<ConsumableBatch[]> => {
    try {
      return await backendDataStore.getConsumableBatches(itemId);
    } catch (error) {
      console.error('Failed to fetch consumable batches:', error);
      return [];
    }
  }, []);

  const getEquipmentRecords = useCallback(async (itemId: string): Promise<EquipmentRecord[]> => {
    try {
      return await backendDataStore.getEquipmentRecords(itemId);
    } catch (error) {
      console.error('Failed to fetch equipment records:', error);
      return [];
    }
  }, []);

  const addInventoryItem = (item: InventoryItem) => {
    setState(prev => ({
      ...prev,
      items: [...prev.items, item],
    }));
  };

  const removeInventoryItem = useCallback((id: string): InventoryItem | null => {
    let removedItem: InventoryItem | null = null;

    setState(prev => {
      const itemToRemove = prev.items.find(item => item.id === id);
      if (itemToRemove) {
        removedItem = itemToRemove;
        return {
          ...prev,
          items: prev.items.filter(item => item.id !== id),
        };
      }
      return prev;
    });

    return removedItem;
  }, []);

  const findItemByName = useCallback((name: string): InventoryItem | null => {
    if (!name || !state.items.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = state.items.find(item =>
      item.name.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = state.items.find(item =>
      item.name.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(item.name.toLowerCase())
    );

    return found || null;
  }, [state.items]);

  // Load initial data only after user is loaded
  useEffect(() => {
    if (!userLoading && user) {
      refreshItems();
    }
  }, [userLoading, user]);

  const contextValue: InventoryContextType = {
    ...state,
    categories,
    categoriesLoading,
    getItemById,
    refreshItems,
    updateQuantity,
    getItemsByCategory,
    getCategories,
    loadCategoriesForBusinessType,
    getRawMaterialBatches,
    getFinishedProductBatches,
    getConsumableBatches,
    getEquipmentRecords,
    addInventoryItem,
    removeInventoryItem,
    findItemByName,
  };

  return (
    <InventoryContext.Provider value={contextValue}>
      {children}
    </InventoryContext.Provider>
  );
};

export const useInventory = (): InventoryContextType => {
  const context = useContext(InventoryContext);
  if (context === undefined) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};
