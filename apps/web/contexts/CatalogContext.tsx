"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CatalogContextState, CatalogItem } from './types';
import { backendDataStore } from '../services/backendDataStore';
import { useUser } from './UserContext';

interface CatalogContextActions {
  selectItem: (item: CatalogItem | null) => void;
  getItemById: (id: string) => CatalogItem | undefined;
  refreshItems: () => Promise<void>;
  addCatalogItem: (item: CatalogItem) => Promise<void>;
  removeCatalogItem: (id: string) => Promise<CatalogItem | null>;
  findCatalogItemByName: (name: string) => CatalogItem | null;
}

type CatalogContextType = CatalogContextState & CatalogContextActions;

const CatalogContext = createContext<CatalogContextType | undefined>(undefined);

interface CatalogProviderProps {
  children: ReactNode;
}

export const CatalogProvider: React.FC<CatalogProviderProps> = ({ children }) => {
  const { user, loading: userLoading } = useUser();
  const [state, setState] = useState<CatalogContextState>({
    items: [],
    selectedItem: null,
    loading: true,
    error: null,
  });

  // Fetch catalog items from backend
  const fetchCatalogItems = async (): Promise<CatalogItem[]> => {
    return await backendDataStore.getCatalogItems();
  };

  const refreshItems = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const items = await fetchCatalogItems();
      setState(prev => ({
        ...prev,
        items,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch catalog items',
      }));
    }
  };

  const selectItem = (item: CatalogItem | null) => {
    setState(prev => ({
      ...prev,
      selectedItem: item,
    }));
  };

  const getItemById = (id: string): CatalogItem | undefined => {
    return state.items.find(item => item.id === id);
  };

  const addCatalogItem = async (item: CatalogItem) => {
    try {
      const newItem = await backendDataStore.addCatalogItem(item);
      setState(prev => ({
        ...prev,
        items: [...prev.items, newItem],
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to add catalog item',
      }));
    }
  };

  const removeCatalogItem = async (id: string): Promise<CatalogItem | null> => {
    const itemToRemove = state.items.find(item => item.id === id);
    if (!itemToRemove) return null;

    try {
      await backendDataStore.deleteCatalogItem(id);
      setState(prev => ({
        ...prev,
        items: prev.items.filter(item => item.id !== id),
        selectedItem: prev.selectedItem?.id === id ? null : prev.selectedItem,
      }));
      return itemToRemove;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to remove catalog item',
      }));
      return null;
    }
  };

  const findCatalogItemByName = (name: string): CatalogItem | null => {
    if (!name || !state.items.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = state.items.find(item =>
      item.productName.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = state.items.find(item =>
      item.productName.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(item.productName.toLowerCase())
    );

    return found || null;
  };

  // Load initial data only after user is loaded
  useEffect(() => {
    if (!userLoading && user) {
      refreshItems();
    }
  }, [userLoading, user]);

  const contextValue: CatalogContextType = {
    ...state,
    selectItem,
    getItemById,
    refreshItems,
    addCatalogItem,
    removeCatalogItem,
    findCatalogItemByName,
  };

  return (
    <CatalogContext.Provider value={contextValue}>
      {children}
    </CatalogContext.Provider>
  );
};

export const useCatalog = (): CatalogContextType => {
  const context = useContext(CatalogContext);
  if (context === undefined) {
    throw new Error('useCatalog must be used within a CatalogProvider');
  }
  return context;
};
