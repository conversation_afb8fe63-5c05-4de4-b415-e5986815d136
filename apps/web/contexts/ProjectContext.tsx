"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ProjectContextState, Project, TeamMember, ProjectTask, ProjectModuleType } from './types';
import { backendDataStore } from '../services/backendDataStore';

interface ProjectContextActions {
  getProjectById: (id: string) => Project | undefined;
  refreshProjects: () => Promise<void>;
  updateProjectStatus: (id: string, status: Project['status']) => void;
  selectProject: (project: Project | null) => void;
  toggleTaskCompletion: (projectId: string, taskId: string) => void;
  addProject: (project: Project) => void;
  removeProject: (id: string) => Project | null;
  findProjectByName: (name: string) => Project | null;
  // Project editing methods
  updateProject: (id: string, updates: Partial<Project>) => void;
  addTeamMemberToProject: (projectId: string, teamMemberId: string) => void;
  removeTeamMemberFromProject: (projectId: string, teamMemberId: string) => void;
  addTaskToProject: (projectId: string, task: Omit<ProjectTask, 'id' | 'projectId'>) => void;
  updateProjectTask: (projectId: string, taskId: string, updates: Partial<ProjectTask>) => void;
  removeTaskFromProject: (projectId: string, taskId: string) => void;
  enableProjectModule: (projectId: string, module: ProjectModuleType) => void;
  disableProjectModule: (projectId: string, module: ProjectModuleType) => void;
  updateProjectModuleConfig: (projectId: string, moduleConfig: any) => void;
}

type ProjectContextType = ProjectContextState & ProjectContextActions & {
  selectedProject: Project | null;
};

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

interface ProjectProviderProps {
  children: ReactNode;
}

export const ProjectProvider: React.FC<ProjectProviderProps> = ({ children }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate API call to load projects
  const loadProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const projects = await backendDataStore.getProjects();
      setProjects(projects);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error loading projects:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load projects on mount
  useEffect(() => {
    loadProjects();
  }, []);

  // Context actions
  const getProjectById = (id: string): Project | undefined => {
    return projects.find(project => project.id === id);
  };

  const refreshProjects = async (): Promise<void> => {
    await loadProjects();
  };

  const updateProjectStatus = (id: string, status: Project['status']): void => {
    setProjects(prev => prev.map(project => 
      project.id === id ? { ...project, status } : project
    ));
  };

  const selectProject = (project: Project | null): void => {
    setSelectedProject(project);
  };

  const toggleTaskCompletion = (projectId: string, taskId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId && project.tasks) {
        const updatedTasks = project.tasks.map(task =>
          task.id === taskId ? { ...task, completed: !task.completed } : task
        );
        const updatedProject = { ...project, tasks: updatedTasks };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const addProject = (project: Project): void => {
    setProjects(prev => [...prev, project]);
  };

  const removeProject = (id: string): Project | null => {
    let removedProject: Project | null = null;

    setProjects(prev => {
      const projectToRemove = prev.find(project => project.id === id);
      if (projectToRemove) {
        removedProject = projectToRemove;

        // If the removed project is currently selected, clear the selection
        if (selectedProject?.id === id) {
          setSelectedProject(null);
        }

        return prev.filter(project => project.id !== id);
      }
      return prev;
    });

    return removedProject;
  };

  const findProjectByName = (name: string): Project | null => {
    if (!name || !projects.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = projects.find(project =>
      project.name.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = projects.find(project =>
      project.name.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(project.name.toLowerCase())
    );

    return found || null;
  };

  // Project editing methods
  const updateProject = (id: string, updates: Partial<Project>): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === id) {
        const updatedProject = { ...project, ...updates };

        // Update selected project if it's the same one
        if (selectedProject?.id === id) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const addTeamMemberToProject = (projectId: string, teamMemberId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          teamMembers: [...project.teamMembers, teamMemberId]
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const removeTeamMemberFromProject = (projectId: string, teamMemberId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          teamMembers: project.teamMembers.filter(id => id !== teamMemberId)
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const addTaskToProject = (projectId: string, task: Omit<ProjectTask, 'id' | 'projectId'>): void => {
    const newTask: ProjectTask = {
      ...task,
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      projectId
    };

    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          tasks: [...(project.tasks || []), newTask]
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const updateProjectTask = (projectId: string, taskId: string, updates: Partial<ProjectTask>): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId && project.tasks) {
        const updatedTasks = project.tasks.map(task =>
          task.id === taskId ? { ...task, ...updates } : task
        );
        const updatedProject = { ...project, tasks: updatedTasks };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const removeTaskFromProject = (projectId: string, taskId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId && project.tasks) {
        const updatedProject = {
          ...project,
          tasks: project.tasks.filter(task => task.id !== taskId)
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const enableProjectModule = (projectId: string, module: ProjectModuleType): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            enabled: project.modules.enabled.includes(module)
              ? project.modules.enabled
              : [...project.modules.enabled, module]
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const disableProjectModule = (projectId: string, module: ProjectModuleType): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            enabled: project.modules.enabled.filter(m => m !== module)
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const updateProjectModuleConfig = (projectId: string, moduleConfig: any): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            configuration: { ...project.modules.configuration, ...moduleConfig }
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const contextValue: ProjectContextType = {
    projects,
    selectedProject,
    loading,
    error,
    getProjectById,
    refreshProjects,
    updateProjectStatus,
    selectProject,
    toggleTaskCompletion,
    addProject,
    removeProject,
    findProjectByName,
    updateProject,
    addTeamMemberToProject,
    removeTeamMemberFromProject,
    addTaskToProject,
    updateProjectTask,
    removeTaskFromProject,
    enableProjectModule,
    disableProjectModule,
    updateProjectModuleConfig,
  };

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};
