"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, BusinessType } from './types';
import { backendDataStore } from '../services/backendDataStore';

interface UserContextState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

interface UserContextActions {
  setBusinessType: (businessType: BusinessType) => void;
  updateUser: (userData: Partial<User>) => void;
  signOut: () => void;
}

type UserContextType = UserContextState & UserContextActions;

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

// Mock user data - in real app this would come from authentication
const mockUser: User = {
  id: 'user-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  businessType: 'fabrication', // Default to fabrication as requested
  companyName: 'Empresa de Fabricación'
};

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [state, setState] = useState<UserContextState>({
    user: null,
    loading: true,
    error: null,
  });

  // Load user data from backend
  const loadUser = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // For now, use the seeded user ID - in a real app this would come from authentication
      const userId = 'cmcl1e6l70000v1kcq5hf8v55';
      backendDataStore.setCurrentUserId(userId);

      const user = await backendDataStore.getCurrentUser();
      if (user) {
        setState(prev => ({
          ...prev,
          user,
          loading: false,
        }));
      } else {
        // Fallback to mock user if not found in backend
        setState(prev => ({
          ...prev,
          user: mockUser,
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load user data',
      }));
    }
  };

  const setBusinessType = (businessType: BusinessType) => {
    setState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, businessType } : null,
    }));
  };

  const updateUser = (userData: Partial<User>) => {
    setState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...userData } : null,
    }));
  };

  const signOut = () => {
    setState({
      user: null,
      loading: false,
      error: null,
    });
  };

  // Load initial user data
  useEffect(() => {
    loadUser();
  }, []);

  const contextValue: UserContextType = {
    ...state,
    setBusinessType,
    updateUser,
    signOut,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
